import dayjs from 'dayjs'
import logger from '../../../../../model/logger/logger'
import { PlannerContext } from './planner_context'
import { LLM } from '../../../../../lib/ai/llm/LLM'
import { PlannerTrigger } from './planner_trigger'
import { TaskManager } from '../task/task_manager'
import { Job, Queue } from 'bullmq'
import { getBotId } from '../../../../../config/chat_id'
import { RedisDB } from '../../../../../model/redis/redis'
import { SchedulePlanItem, TaskScheduler } from '../task/task_scheduler'
import { listSOPByChatId } from '../../flow/schedule/task_starter'
import { SalesNodeHelper } from '../../flow/helper/salesNodeHelper'
import { ITask, PlanOperations, PlanResponse } from '../types'
import { RedisCacheDB } from '../../../../../model/redis/redis_cache'
import { PrismaMongoClient } from '../../../../../model/mongodb/prisma'
import { TaskWorker } from '../task/task_worker'
import { TaskStatus } from '@prisma/client'
import { JSONHelper } from '../../../../../lib/json/json'
import { IWorkflowState } from '../../flow/flow'
import { ChatInterruptHandler } from '../../message/interrupt_handler'
import { getPrompt } from '../../agent/prompt'
import { ContextBuilder } from '../../agent/context'

interface IScheduleTask {
  task_id: string
  urgency_level: 'urgent' | 'normal'
  task_type: 'daily_greeting' | 'pre_class_reminder' | 'post_class_follow_up' | 'engagement_prompt' | 'value_delivery'
  scheduled_time: string // YYYY-MM-DD HH:MM:SS 或 "now"

  description: string // 任务描述
  chat_id: string
}

export class Planner {
  public static async checkAndCreatePlan(state: IWorkflowState) {
    const { chat_id, userMessage, round_id } = state

    const chatHistory = await PlannerContext.getChatHistory(chat_id, userMessage)

    const trigger = await new PlannerTrigger({ chat_id, round_id }).detectTrigger({
      conversationHistory: chatHistory,
      currentUserMessage: userMessage
    })
    if (!trigger.isPlannerTrigger) {
      return null
    }

    logger.log({ chat_id }, userMessage, '触发 Planner: ', { reasoning: trigger.reasoning })

    // 允许 Planner 被打断
    await state.interruptHandler.interruptCheck()

    const plan = await this.generatePlan(chat_id, round_id, userMessage, state.interruptHandler)

    // 处理计划操作
    await this.executePlanOperations(chat_id, plan.plans, plan.think, round_id)

    logger.log({ chat_id }, '创建计划', JSON.stringify(plan, null, 4))

    return plan
  }

  public static async generatePlan(chat_id: string, round_id: string, userMessage: string, interruptHandler?: ChatInterruptHandler) {
    const plannerPrompt = await getPrompt('planner')
    const randomTemperature = Number((Math.random() * 0.3 + 0.7).toFixed(2))
    const plannerRAG = await PlannerContext.getPlannerRAG(chat_id, userMessage, round_id)
    const activeTasks = await TaskManager.getStringifyActiveTasks(chat_id)
    const customerBehavior = await ContextBuilder.getCustomerBehavior(chat_id)
    const customerPortrait = await ContextBuilder.getCustomerPortrait(chat_id)
    const dialogHistory = await SalesNodeHelper.getChatHistory(chat_id, 6, 18)
    const temporalInformation = await ContextBuilder.getTimeInformation(chat_id)

    if (interruptHandler) {
      await interruptHandler.interruptCheck()
    }

    const output = await LLM.predict(
      plannerPrompt, {
        temperature: randomTemperature,
        response_json: true,
        meta: {
          promptName: 'planner',
          chat_id: chat_id,
          round_id: round_id,
        }
      }, {
        plannerRAG: plannerRAG,
        activeTasks: activeTasks,
        customerBehavior: customerBehavior,
        customerPortrait: customerPortrait,
        dialogHistory: dialogHistory,
        temporalInformation: temporalInformation,
      })

    let think: string = ''
    let plans: PlanOperations = { toAdd: [], toUpdate: [], toRemove: [] }

    try {
      const parsedOutput = JSONHelper.parse(output) as PlanResponse
      think = parsedOutput.think
      plans = parsedOutput.plans
    } catch (error) {
      logger.error('Planner 解析 JSON 失败:', error)
    }

    logger.debug({ chat_id: chat_id, round_id: round_id }, `think: ${think}\nplans: ${JSON.stringify(plans)}`)
    return { think, plans }
  }

  /**
   * 执行计划操作：新增、更新、删除任务
   * @param chat_id 聊天ID
   * @param planOperations 计划操作
   * @param overall_goal 总体目标
   * @param round_id 轮次ID
   */
  private static async executePlanOperations(
    chat_id: string,
    planOperations: PlanOperations,
    overall_goal: string,
    round_id: string
  ) {
    // 获取当前活跃任务，用于ID映射
    const activeTasks = await TaskManager.getActiveTasks(chat_id)

    // 1. 处理新增任务
    if (planOperations.toAdd.length > 0) {
      // 当任务总数 > 5 条时，拒绝添加操作
      if (activeTasks.length < 5) {
        await TaskManager.createTasks(chat_id, planOperations.toAdd, overall_goal, round_id)
      }

      // logger.log({ chat_id }, `新增任务: ${planOperations.toAdd.length} 个`)
    }

    // 2. 处理更新任务
    if (planOperations.toUpdate.length > 0) {
      const updates = planOperations.toUpdate
        .map((update) => {
          const taskIndex = parseInt(update.id, 10) - 1 // 转换为数组索引
          if (taskIndex >= 0 && taskIndex < activeTasks.length) {
            return {
              task_id: activeTasks[taskIndex].id,
              description: update.content
            }
          }
          return null
        })
        .filter((update) => update !== null) as Array<{ task_id: string, description: string }>

      if (updates.length > 0) {
        await TaskManager.updateTaskDescriptions(updates)
        // logger.log({ chat_id }, `更新任务: ${updates.length} 个`)
      }
    }

    // 3. 处理删除任务
    if (planOperations.toRemove.length > 0) {
      const taskIdsToRemove = planOperations.toRemove
        .map((id) => {
          const taskIndex = parseInt(id, 10) - 1 // 转换为数组索引
          if (taskIndex >= 0 && taskIndex < activeTasks.length) {
            return activeTasks[taskIndex].id
          }
          return null
        })
        .filter((id) => id !== null) as string[]

      if (taskIdsToRemove.length > 0) {
        await TaskManager.cancelTasks(taskIdsToRemove)
        // logger.log({ chat_id }, `删除任务: ${taskIdsToRemove.length} 个`)
      }
    }
  }

  /**
   * 将任务转为定时发送的 SOP
   * @param chat_id
   * @param tasks
   */
  public static async taskSchedule(chat_id: string, tasks:  ITask[]) {
    // context 拼装
    const currentTime = await ContextBuilder.getTimeInformation(chat_id)

    const tasksToSchedule = tasks
      .filter((task) => task.send_time === null) // 已经规划过的任务，不再进行二次规划
      .map((task) => {
        return {
          task_id: task.id.slice(-4),
          task_description: task.description
        }
      })

    // 获取 visualized sop 和 planner 消息队列
    const existing_schedule = await this.filterSOPByDate(chat_id, new Date(), new Date(Date.now() + 3 * 24 * 60 * 60 * 1000))

    const customerBehavior = await ContextBuilder.getCustomerPortrait(chat_id)
    const dialogHistory = await SalesNodeHelper.getChatHistory(chat_id, 3, 10)

    const scheduledTask =  await new TaskScheduler(chat_id).scheduleTask({
      user_profile: `${customerBehavior}\n\n${  dialogHistory}`,
      current_time: currentTime,
      tasks_to_schedule: tasksToSchedule,
      existing_schedule: existing_schedule
    })

    // 对 task Id 进行还原
    const idMap = tasks.reduce((map, task) => {
      map[task.id.slice(-4)] = task.id
      return map
    }, {} as Record<string, string>)


    // 假设 scheduledTask 是 [{ task_id: 'a1b2', ... }, ...]
    const scheduledTasks = scheduledTask.map((item) => {
      return {
        ...item,
        task_id: idMap[item.task_id] // 还原完整 id
      }
    })

    // 更新发送时间
    await Promise.allSettled(scheduledTasks.map(async (task) => {
      await TaskManager.updateTask(task.task_id, { send_time: task.scheduled_time })
    }))


    logger.log('规划任务时间', JSON.stringify(scheduledTasks, null, 4))
    return scheduledTasks
  }

  /**
   * 添加延迟任务到消息队列
   * @param chat_id
   * @param tasks
   */
  public static async addDelayedTask(chat_id: string, tasks: SchedulePlanItem[]) {
    // 过滤掉 now 的任务
    const delayedTasks = tasks.filter((item) => item.scheduled_time !== 'now')

    const queue = new Queue<IScheduleTask>(this.getPlannerSOPQueueName(getBotId(chat_id)), {
      connection: RedisDB.getInstance()
    })

    const jobs: {name: string, data: any, opts: any}[] = []

    // 通过 task_id 查询出原始的 task
    for (const delayedTask of delayedTasks) {
      const task = await TaskManager.getTaskById(delayedTask.task_id)

      if (!task) continue
      if (task.status !== 'TODO') continue

      try {
        jobs.push({
          name: task.description,
          data: task,
          opts: { delay: new Date(delayedTask.scheduled_time).getTime() - Date.now() }
        })
      } catch (e) {
        logger.error(e)
        continue
      }
    }

    await queue.addBulk(jobs)
  }

  public static getPlannerSOPQueueName(botId:string) {
    return `moer-planner-sop-${botId}`
  }

  private static async listSOPByChatId(chatId: string): Promise<Job[]> {
    const queue = new Queue<IScheduleTask>(this.getPlannerSOPQueueName(getBotId(chatId)), {
      connection: RedisDB.getInstance()
    })

    const allJobs = await queue.getDelayed()
    return allJobs.filter((item) => item.data.chat_id === chatId)
  }


  public static async filterSOPByDate(chatId: string, startDate: Date, endDate: Date) {
    const sops = await listSOPByChatId(chatId)
    const plannerSOPS = await Planner.listSOPByChatId(chatId)

    // 获取从开始日期到 endDate 期间的 job
    const filteredSOPs = sops.filter((item) => {
      const jobTime = new Date(item.delay + item.timestamp)
      return jobTime >= startDate && jobTime <= endDate
    })

    const filteredPlannerSOPs = plannerSOPS.filter((item) => {
      const jobTime = new Date(item.delay + item.timestamp)
      return jobTime >= startDate && jobTime <= endDate
    })

    // redis 中取回对应的 title
    const sopValue = await new RedisCacheDB(`moer:${getBotId(chatId)}:visualized_sop`).get()

    // 构建 SOP Map
    const sopMap = new Map()
    for (const sop of sopValue) {
      sopMap.set(sop.id, sop.title)
    }

    for (const sop of filteredSOPs) {
      if (!sopMap.has(sop.name)) {
        logger.error('没有这个sop', sop.name)
        continue
      }
      sop.name = sopMap.get(sop.name)
    }

    // 合并 并 以 时间排序
    const mergedSOPs = filteredSOPs.map((item) => {
      return {
        description: item.name,
        time: new Date(item.delay + item.timestamp)
      }
    }).concat(filteredPlannerSOPs.map((item) => {
      return {
        description: item.name,
        time: new Date(item.delay + item.timestamp)
      }
    })).sort((a, b) => a.time.getTime() - b.time.getTime())

    // 描述 + 时间
    return mergedSOPs.map((item) => {
      return {
        description: item.description,
        time: dayjs(item.time).format('YYYY-MM-DD HH:mm:ss')
      }
    })
  }


  static async executeImmediateTask(chat_id: string, scheduledTasks: SchedulePlanItem[]) {
    // 获取任务描述进行合并
    // 执行任务
    // 更新任务状态
    const immediateTasks = scheduledTasks.filter((item) => item.scheduled_time === 'now')

    const tasks = await PrismaMongoClient.getInstance().task.findMany({
      where: {
        chat_id,
        id: {
          in: immediateTasks.map((item) => item.task_id)
        }
      }
    })

    // 合并 Task 描述
    const taskDescription = tasks.filter((item) => item.status === 'TODO').map((item, index) => `${index + 1}. ${item.description}`).join('\n')

    // 更新任务状态
    await Promise.all(tasks.map((task) => TaskManager.updateStatus(task.id, TaskStatus.DONE)))

    await TaskWorker.processTask(chat_id, taskDescription)
  }
}